import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  Activity,
  Zap,
  BarChart3,
  Eye,
  CheckCircle2,
  ArrowRight,
  Star,
  Users,
  ExternalLink
} from "lucide-react";
import { Link } from "react-router-dom";

const Index = () => {
  const features = [
    {
      icon: <Activity className="w-6 h-6" />,
      title: "Real-Time Monitoring",
      description: "Monitor API health, latency, and error rates across all your providers with live dashboards and alerts."
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Intelligent Failover",
      description: "Automatic failover to backup providers when issues are detected, ensuring zero downtime for critical services."
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: "AI-Powered Resilience",
      description: "Machine learning algorithms predict outages and anomalies before they impact your users."
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Advanced Analytics",
      description: "Comprehensive performance analytics and SLA reporting to optimize your API strategy."
    }
  ];

  const stats = [
    { label: "APIs Monitored", value: "10K+" },
    { label: "Uptime Improved", value: "99.9%" },
    { label: "Companies Trust Us", value: "500+" },
    { label: "Incidents Prevented", value: "2.5K+" }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/95 sticky top-0 z-50">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-cyber rounded-xl flex items-center justify-center shadow-glow transition-transform hover:scale-105">
                <Shield className="w-7 h-7 text-foreground" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">PulseMesh</h1>
                <p className="text-sm text-muted-foreground">API Resilience Platform</p>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <Button variant="ghost" size="sm" className="hidden sm:flex">
                <ExternalLink className="w-4 h-4 mr-2" />
                GitHub
              </Button>
              <Link to="/dashboard">
                <Button variant="cyber" className="group">
                  Get Started
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-24 px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <div className="max-w-5xl mx-auto">
            <div className="animate-in fade-in slide-in-from-bottom-4 duration-1000">
              <Badge className="mb-8 bg-primary/20 text-primary border-primary/30 px-4 py-2 text-sm font-medium">
                <Star className="w-4 h-4 mr-2" />
                Enterprise-Grade API Resilience
              </Badge>
            </div>

            <div className="animate-in fade-in slide-in-from-bottom-6 duration-1000 delay-200">
              <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold text-foreground mb-8 leading-[0.9] tracking-tight">
                Bulletproof Your
                <br />
                <span className="bg-gradient-cyber bg-clip-text text-transparent">API Infrastructure</span>
              </h1>
            </div>

            <div className="animate-in fade-in slide-in-from-bottom-8 duration-1000 delay-400">
              <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed font-light">
                Intelligent middleware platform that monitors, predicts, and automatically handles API failures
                across multiple providers. Never lose a transaction again.
              </p>
            </div>

            <div className="animate-in fade-in slide-in-from-bottom-10 duration-1000 delay-600">
              <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
                <Link to="/dashboard">
                  <Button variant="cyber" size="lg" className="group px-8 py-4 text-lg">
                    <Shield className="w-6 h-6 mr-3 transition-transform group-hover:scale-110" />
                    Try Live Demo
                    <ArrowRight className="w-5 h-5 ml-3 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg group">
                  <Users className="w-6 h-6 mr-3 transition-transform group-hover:scale-110" />
                  Book Demo
                </Button>
              </div>
            </div>

            {/* Stats */}
            <div className="animate-in fade-in slide-in-from-bottom-12 duration-1000 delay-800">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-12 max-w-4xl mx-auto">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center group">
                    <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-3 transition-transform group-hover:scale-105">
                      {stat.value}
                    </div>
                    <div className="text-sm md:text-base text-muted-foreground font-medium">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 px-6 lg:px-8 bg-card/30">
        <div className="container mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Built for Mission-Critical
              <br />
              <span className="text-primary">Applications</span>
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-light">
              Comprehensive API resilience with intelligent monitoring, predictive analytics,
              and automated failover systems.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="p-8 hover:shadow-glow transition-all duration-500 border-border group hover:border-primary/50 bg-card/50 backdrop-blur"
              >
                <div className="w-16 h-16 bg-primary/20 rounded-xl flex items-center justify-center mb-6 text-primary transition-transform group-hover:scale-110 group-hover:bg-primary/30">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-4 group-hover:text-primary transition-colors">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-8 leading-tight">
              Ready to Eliminate
              <br />
              <span className="bg-gradient-cyber bg-clip-text text-transparent">API Downtime?</span>
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 leading-relaxed font-light">
              Join hundreds of companies protecting their revenue with intelligent API resilience.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
              <Link to="/dashboard">
                <Button variant="cyber" size="lg" className="group px-8 py-4 text-lg">
                  <Shield className="w-6 h-6 mr-3 transition-transform group-hover:scale-110" />
                  Start Free Trial
                  <ArrowRight className="w-5 h-5 ml-3 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg group">
                <Users className="w-6 h-6 mr-3 transition-transform group-hover:scale-110" />
                Schedule Demo
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-5 h-5 text-green-500" />
                <span className="text-base">14-day free trial</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-5 h-5 text-green-500" />
                <span className="text-base">No credit card required</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-5 h-5 text-green-500" />
                <span className="text-base">Setup in 5 minutes</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border py-16 px-6 lg:px-8 bg-card/50">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="w-12 h-12 bg-gradient-cyber rounded-xl flex items-center justify-center shadow-glow">
              <Shield className="w-7 h-7 text-foreground" />
            </div>
            <div className="text-left">
              <span className="text-2xl font-bold text-foreground">PulseMesh</span>
              <p className="text-sm text-muted-foreground">API Resilience Platform</p>
            </div>
          </div>
          <p className="text-muted-foreground">
            © 2024 PulseMesh. Built for enterprise API management.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
