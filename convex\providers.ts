import { v } from 'convex/values';
import { query, mutation } from './_generated/server';
import { getAuthUserId } from '@convex-dev/auth/server';

// Get all providers for the current user
export const getProviders = query({
	args: {},
	handler: async (ctx) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		return await ctx.db
			.query('providers')
			.withIndex('by_created_by', (q) => q.eq('createdBy', userId))
			.collect();
	},
});

// Get a specific provider by ID
export const getProvider = query({
	args: { id: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.id);
		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		return provider;
	},
});

// Create a new provider
export const createProvider = mutation({
	args: {
		name: v.string(),
		type: v.union(
			v.literal('payment'),
			v.literal('sms'),
			v.literal('email'),
			v.literal('maps'),
			v.literal('ai'),
			v.literal('storage'),
			v.literal('auth'),
			v.literal('video'),
			v.literal('realtime'),
			v.literal('analytics'),
			v.literal('monitoring')
		),
		endpoint: v.string(),
		priority: v.number(),
		isPrimary: v.optional(v.boolean()),
		configuration: v.optional(
			v.object({
				timeout: v.optional(v.number()),
				retries: v.optional(v.number()),
				headers: v.optional(v.any()),
			})
		),
	},
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const now = Date.now();

		return await ctx.db.insert('providers', {
			...args,
			createdBy: userId,
			isHealthy: true,
			latency: 0,
			errorRate: 0,
			lastCheck: now,
			createdAt: now,
			updatedAt: now,
		});
	},
});

// Update a provider
export const updateProvider = mutation({
	args: {
		id: v.id('providers'),
		name: v.optional(v.string()),
		endpoint: v.optional(v.string()),
		priority: v.optional(v.number()),
		isPrimary: v.optional(v.boolean()),
		configuration: v.optional(
			v.object({
				timeout: v.optional(v.number()),
				retries: v.optional(v.number()),
				headers: v.optional(v.any()),
			})
		),
	},
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const { id, ...updates } = args;
		const provider = await ctx.db.get(id);

		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		await ctx.db.patch(id, {
			...updates,
			updatedAt: Date.now(),
		});
	},
});

// Delete a provider
export const deleteProvider = mutation({
	args: { id: v.id('providers') },
	handler: async (ctx, args) => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error('Not authenticated');
		}

		const provider = await ctx.db.get(args.id);
		if (!provider || provider.createdBy !== userId) {
			throw new Error('Provider not found or access denied');
		}

		await ctx.db.delete(args.id);
	},
});

// Update provider health status
export const updateProviderHealth = mutation({
	args: {
		id: v.id('providers'),
		isHealthy: v.boolean(),
		latency: v.number(),
		errorRate: v.number(),
	},
	handler: async (ctx, args) => {
		const { id, ...healthData } = args;

		await ctx.db.patch(id, {
			...healthData,
			lastCheck: Date.now(),
			updatedAt: Date.now(),
		});
	},
});
